from langchain_community.document_loaders import SQLDatabaseLoader
from langchain_community.utilities import SQLDatabase

from llm.minstone_llm import LLMFactory

if __name__ == "__main__":
    db = SQLDatabase.from_uri("mysql+pymysql://root:minstone123@192.168.2.154:3306/law_item_sc")
    sql = """
SELECT
  d.ID,
	b.LAW_NAME law_name,
	d.CONTENT_INFO content_info,
	CAST(
		SUBSTRING( d.global_sort, 7, 3 ) AS UNSIGNED
	) AS 条,
	CAST(
		SUBSTRING( d.global_sort, 10, 2 ) AS UNSIGNED
	) AS 款,
	CAST(
		SUBSTRING( d.global_sort, 12, 2 ) AS UNSIGNED
	) AS 项,
	CAST(
		SUBSTRING( d.global_sort, 14, 2 ) AS UNSIGNED
	) AS 目
FROM
	appr_law_detail_info d
	LEFT JOIN appr_law_base_info b ON d.LAW_ID = b.ID
WHERE
	d.id IN (
	SELECT
		CONTENT_ID
	FROM
		appr_matter_struct_law
	WHERE
	FOLDER_ID IN ( SELECT id FROM appr_matter_base_info WHERE BUSINESSCODES = '25' ))
	AND CONTENT_FOLDER_TYPE >3
ORDER BY
	LAW_ID,
	d.global_sort
    """
    loader = SQLDatabaseLoader(
        db=db,
        query=sql,
        page_content_mapper=lambda row: row.get("content_info"),
        metadata_mapper=lambda row: {
            "法律名称": row.get("law_name"),
            "条": row.get("条"),
            "款": row.get("款"),
            "项": row.get("项"),
            "目": row.get("目")
        }
    )
    docs = loader.load()

    # 提取文档对应的ID列表
    from sqlalchemy import text
    with db._engine.connect() as connection:
        query_text = text(sql)
        result = connection.execute(query_text)
        doc_ids = [str(row[0]) for row in result]  # 第一列是 ID

    print(f"提取到 {len(doc_ids)} 个文档ID")

    if len(docs) != len(doc_ids):
        print(f"警告：文档数量 ({len(docs)}) 与ID数量 ({len(doc_ids)}) 不匹配")
        # 如果数量不匹配，使用文档索引作为备用ID
        doc_ids = [f"doc_{i}" for i in range(len(docs))]

    vector_store = LLMFactory.get_instance().get_law_vector_store()
    vector_store.add_documents(docs, ids=doc_ids)
    print(f"成功添加 {len(docs)} 个文档到向量存储，使用自定义ID")
