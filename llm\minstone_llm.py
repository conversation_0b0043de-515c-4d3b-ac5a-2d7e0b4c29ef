import os
from abc import abstractmethod
from concurrent.futures import ThreadPoolExecutor

from dotenv import load_dotenv
from langchain_community.embeddings import DashScopeEmbeddings, HuggingFaceEmbeddings
from langchain_milvus import Milvus
from langchain_openai import ChatOpenAI
from langchain_community.chat_models import ChatOllama
from langchain_core.embeddings import Embeddings
from tenacity import retry, stop_after_attempt, wait_exponential, retry_if_exception_type
from typing import List
import logging
import requests


class OllamaEmbeddings(Embeddings):
    """通过 TCP 访问 Ollama 服务的自定义嵌入类"""

    def __init__(
            self,
            base_url: str = "http://localhost:11434",
            model: str = "nomic-embed-text",
            timeout: int = 60,
            batch_size: int = 8,
            dimensions: int = 768
    ):
        """
        :param base_url: Ollama 服务的 TCP 地址
        :param model: 使用的嵌入模型名称
        :param timeout: 请求超时时间（秒）
        :param batch_size: 批量处理大小
        :param dimensions: 嵌入向量维度
        """
        self.base_url = base_url.rstrip('/')
        self.model = model
        self.timeout = timeout
        self.batch_size = batch_size
        self.dimensions = dimensions
        self.endpoint = f"{self.base_url}/api/embeddings"
        self.session = requests.Session()

        # 配置重试策略
        self.session.mount('http://', requests.adapters.HTTPAdapter(
            max_retries=3,
            pool_connections=10,
            pool_maxsize=100
        ))

        logging.info(f"初始化 Ollama 嵌入服务: {self.endpoint}, 模型: {self.model}")

    @retry(
        stop=stop_after_attempt(3),
        wait=wait_exponential(multiplier=1, min=2, max=10),
        retry=retry_if_exception_type(requests.exceptions.RequestException)
    )
    def _embed(self, text: str) -> List[float]:
        """嵌入单个文本"""
        payload = {
            "model": self.model,
            "prompt": text
        }

        try:
            response = self.session.post(
                self.endpoint,
                json=payload,
                timeout=self.timeout
            )
            response.raise_for_status()
            return response.json()["embedding"]
        except Exception as e:
            logging.error(f"嵌入请求失败: {str(e)}")
            raise

    def embed_documents(self, texts: List[str]) -> List[List[float]]:
        """嵌入多个文档"""
        embeddings = []

        # 分批处理以提高效率
        for i in range(0, len(texts), self.batch_size):
            batch = texts[i:i + self.batch_size]

            # 使用线程池并行处理
            with ThreadPoolExecutor() as executor:
                batch_embeddings = list(executor.map(self._embed, batch))

            embeddings.extend(batch_embeddings)

        return embeddings

    def embed_query(self, text: str) -> List[float]:
        """嵌入查询文本"""
        return self._embed(text)

    def get_embedding_dimensions(self) -> int:
        """获取嵌入维度"""
        return self.dimensions


class LLMFactory:
    def __init__(self):
        load_dotenv()

    # 获取工厂实例
    @classmethod
    def get_instance(cls):
        return MinstoneLLMFactory()

    # 获取嵌入模型的客户端
    @abstractmethod
    def get_embedding_model(self):
        pass

    # 获取聊天模型的客户端
    @abstractmethod
    def get_chat_model(self):
        pass

    # 获取向量存储
    @abstractmethod
    def get_law_vector_store(self):
        pass

    # 获取向量存储
    @abstractmethod
    def get_case_vector_store(self):
        pass

class MinstoneLLMFactory(LLMFactory):
    # 获取嵌入模型的客户端
    def get_embedding_model(self):
        return OllamaEmbeddings(
            base_url="http://192.168.102.10:11434",  # Ollama 服务地址
            model="dengcao/Qwen3-Embedding-4B:Q5_K_M",
            dimensions=1024,
            batch_size=16
        )
        # return DashScopeEmbeddings(
        #     model="text-embedding-v3",
        #     dashscope_api_key=os.getenv("DASHSCOPE_API_KEY")
        # )
        # return HuggingFaceEmbeddings(
        #     model_name="/Users/<USER>/.cache/modelscope/hub/models/fengshan/ChatLaw-Text2Vec",  # 中文法律文本优化模型
        #     model_kwargs={"device": "cpu"}  # 使用 CPU 或 "cuda"
        # )

    # 获取聊天模型的客户端
    def get_chat_model(self):
        return ChatOllama(
            base_url="http://192.168.102.10:11434",  # Ollama 服务的 TCP 地址
            model="deepseek-r1:32b",  # 使用的模型
            temperature=0.1,
            num_ctx=8192  # 上下文长度
        )
        # return ChatOpenAI(openai_api_key=os.getenv("DASHSCOPE_API_KEY"),
        #                   base_url=os.getenv("DASHSCOPE_API_URL"),
        #                   model="qwen-plus")

    # 获取向量存储
    def get_law_vector_store(self):
        return Milvus(embedding_function=self.get_embedding_model(), auto_id=False,
                      enable_dynamic_field=True,
                      collection_name="laws",
                      connection_args={
                          "uri": os.getenv("MILVUS_URL")
                      })

    def get_case_vector_store(self):
        return Milvus(embedding_function=self.get_embedding_model(), auto_id=True,
                      enable_dynamic_field=True,
                      collection_name="cases",
                      connection_args={
                          "uri": os.getenv("MILVUS_URL")
                      })
