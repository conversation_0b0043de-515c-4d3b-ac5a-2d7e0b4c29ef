import uvicorn
from fastapi import FastAPI
from langchain_core.runnables import chain
from langserve import add_routes
from pydantic import BaseModel
from starlette.middleware.cors import CORSMiddleware
from langchain_core.runnables import RunnableLambda

from app import agent
from chains.structure_legal_chain import structure_legal_chain

# 创建FastAPI应用
app = FastAPI(
    title="行政执法智能体API",
    description="提供行政执法领域的法律咨询、类案推荐等服务"
)

# 配置 CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # 允许所有端跨域
    allow_credentials=True,
    allow_methods=["*"],  # 允许所有方法
    allow_headers=["*"],  # 允许所有头部
)


# 定义API请求模型
class QueryRequest(BaseModel):
    input: str


# 定义API响应模型
class QueryResponse(BaseModel):
    output: str


# 添加流式API端点
add_routes(
    app,
    agent.with_config({"run_name": "LawAgent"}),
    path="/law-agent",
    enable_feedback_endpoint=True,
    input_type=QueryRequest,
    output_type=QueryResponse
)

class InputModel(BaseModel):
    question: str
    department_code: str=None

@chain
def legal_wrapper(data: InputModel):
    print("legal_wrapper")
    print(data)
    """包装工作流输入输出"""
    return structure_legal_chain.invoke({"input": data["question"], "department_code": data["department_code"]})


# 添加LangServe路由
add_routes(
    app,
    legal_wrapper,
    path="/legal-consult",
    enable_feedback_endpoint=True,
    input_type=InputModel,
    output_type=dict
)

if __name__ == "__main__":
    uvicorn.run(app, host="0.0.0.0", port=8000)
