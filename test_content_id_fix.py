#!/usr/bin/env python3
"""
测试修复后的 content_id 解析功能和SQL构建
"""

import ast
from typing import List

def test_content_id_parsing():
    """测试不同格式的查询结果解析"""

    # 模拟您遇到的查询结果
    test_result = "[('02bfba82ffda41b184c1406c28378fdb',), ('0c7078fb5ad54c77b4c1a80263da7c7f',)]"

    print(f"原始查询结果: {test_result}")
    print(f"结果类型: {type(test_result)}")

    content_ids = []

    # 如果结果是字符串，需要先解析
    if isinstance(test_result, str):
        try:
            # 尝试将字符串解析为Python对象
            parsed_result = ast.literal_eval(test_result)
            print(f"解析后的结果: {parsed_result}")
            print(f"解析后的类型: {type(parsed_result)}")

            # 处理解析后的结果
            if isinstance(parsed_result, list):
                for row in parsed_result:
                    print(f"处理行: {row}, 类型: {type(row)}")
                    if isinstance(row, tuple) and len(row) > 0:
                        content_ids.append(row[0])
                        print(f"添加 content_id: {row[0]}")
                    elif isinstance(row, dict) and 'CONTENT_ID' in row:
                        content_ids.append(row['CONTENT_ID'])
                    elif isinstance(row, str):
                        # 如果直接是字符串，直接添加
                        content_ids.append(row)
        except (ValueError, SyntaxError) as e:
            print(f"解析查询结果失败: {e}")
            return []
    else:
        # 如果结果不是字符串，按原来的方式处理
        for row in test_result:
            print(f"处理行: {row}")
            if isinstance(row, tuple) and len(row) > 0:
                content_ids.append(row[0])
            elif isinstance(row, dict) and 'CONTENT_ID' in row:
                content_ids.append(row['CONTENT_ID'])

    print(f"最终获取到 {len(content_ids)} 个 CONTENT_ID: {content_ids}")
    return content_ids

def test_sql_construction(content_ids):
    """测试SQL构建"""
    print(f"\n=== 测试SQL构建 ===")
    print(f"Content IDs: {content_ids}")

    # 原来的错误方式（没有引号）
    old_id_list_str = ','.join(map(str, content_ids))
    print(f"错误的SQL片段: WHERE amsl.CONTENT_ID IN ({old_id_list_str})")

    # 修复后的方式（添加引号）
    new_id_list_str = ','.join([f"'{id_}'" for id_ in content_ids])
    print(f"正确的SQL片段: WHERE amsl.CONTENT_ID IN ({new_id_list_str})")

def test_original_problem():
    """测试原始问题：直接迭代字符串"""
    test_result = "[('02bfba82ffda41b184c1406c28378fdb',), ('0c7078fb5ad54c77b4c1a80263da7c7f',)]"

    print("\n=== 原始问题演示 ===")
    print(f"直接迭代字符串结果:")
    for i, char in enumerate(test_result):
        if i < 10:  # 只显示前10个字符
            print(f"字符 {i}: '{char}'")
        else:
            print("...")
            break

if __name__ == "__main__":
    print("=== 测试修复后的解析功能 ===")
    content_ids = test_content_id_parsing()

    if content_ids:
        test_sql_construction(content_ids)

    test_original_problem()
