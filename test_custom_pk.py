#!/usr/bin/env python3
"""
测试使用 original_sql 查询出来的 id 作为向量数据库的 pk 字段
"""

import logging
from langchain_community.utilities import SQLDatabase
from datarw.db_datasource_processor import create_enhanced_documents, create_original_documents
from llm.minstone_llm import LLMFactory

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


def test_custom_pk_implementation():
    """测试自定义主键实现"""
    
    print("=== 测试自定义主键实现 ===")
    
    # 连接数据库
    db = SQLDatabase.from_uri("mysql+pymysql://root:minstone123@192.168.2.154:3306/law_item_sc")
    
    try:
        # 1. 测试增强文档创建方法
        print("\n1. 测试增强文档创建方法...")
        docs, doc_ids = create_enhanced_documents(db)
        print(f"创建了 {len(docs)} 个文档")
        print(f"提取了 {len(doc_ids)} 个ID")
        
        if len(docs) > 0:
            print(f"第一个文档ID: {doc_ids[0]}")
            print(f"第一个文档内容预览: {docs[0].page_content[:100]}...")
            print(f"第一个文档元数据: {list(docs[0].metadata.keys())}")
        
        # 2. 测试向量存储配置
        print("\n2. 测试向量存储配置...")
        vector_store = LLMFactory.get_instance().get_law_vector_store()
        print(f"向量存储类型: {type(vector_store)}")
        
        # 检查是否支持自定义ID
        try:
            # 测试添加少量文档
            test_docs = docs[:2] if len(docs) >= 2 else docs
            test_ids = doc_ids[:2] if len(doc_ids) >= 2 else doc_ids
            
            print(f"\n3. 测试添加文档（使用自定义ID）...")
            print(f"测试文档数量: {len(test_docs)}")
            print(f"测试ID: {test_ids}")
            
            # 尝试添加文档
            vector_store.add_documents(test_docs, ids=test_ids)
            print("✓ 成功添加文档到向量存储（使用自定义ID）")
            
            # 4. 测试查询
            print("\n4. 测试查询...")
            if len(test_docs) > 0:
                query_text = test_docs[0].page_content[:50]  # 使用第一个文档的部分内容作为查询
                results = vector_store.similarity_search(query_text, k=1)
                
                if results:
                    print(f"查询结果数量: {len(results)}")
                    print(f"查询结果内容预览: {results[0].page_content[:100]}...")
                    print("✓ 查询功能正常")
                else:
                    print("⚠ 查询未返回结果")
            
        except Exception as add_error:
            print(f"✗ 添加文档失败: {add_error}")
            
            # 尝试不使用自定义ID
            print("\n尝试不使用自定义ID...")
            try:
                vector_store_auto = LLMFactory.get_instance().get_law_vector_store()
                vector_store_auto.add_documents(test_docs)
                print("✓ 使用自动ID成功添加文档")
            except Exception as auto_error:
                print(f"✗ 使用自动ID也失败: {auto_error}")
        
    except Exception as e:
        logger.error(f"测试过程中发生错误: {e}")
        import traceback
        logger.error(f"详细错误信息: {traceback.format_exc()}")
        
        # 测试原始方法
        print("\n尝试使用原始方法...")
        try:
            docs, doc_ids = create_original_documents(db)
            print(f"原始方法创建了 {len(docs)} 个文档，{len(doc_ids)} 个ID")
            
            if len(docs) > 0:
                print(f"第一个文档ID: {doc_ids[0]}")
                
        except Exception as original_error:
            print(f"原始方法也失败了: {original_error}")


def test_id_extraction():
    """测试ID提取功能"""
    
    print("\n=== 测试ID提取功能 ===")
    
    # 连接数据库
    db = SQLDatabase.from_uri("mysql+pymysql://root:minstone123@192.168.2.154:3306/law_item_sc")
    
    # 测试SQL查询
    test_sql = """
    SELECT
        d.ID AS id,
        b.LAW_NAME AS law_name,
        d.CONTENT_INFO AS content_info
    FROM appr_law_detail_info d
    JOIN appr_law_base_info b ON d.LAW_ID = b.ID
    JOIN appr_matter_struct_law s ON d.ID = s.CONTENT_ID
    JOIN appr_matter_base_info m ON s.FOLDER_ID = m.id
    WHERE m.BUSINESSCODES = '25'
    AND d.CONTENT_FOLDER_TYPE > 3
    ORDER BY d.LAW_ID, d.global_sort
    LIMIT 5;
    """
    
    try:
        from sqlalchemy import text
        with db._engine.connect() as connection:
            query_text = text(test_sql)
            result = connection.execute(query_text)
            
            print("查询结果:")
            for i, row in enumerate(result):
                print(f"  行 {i+1}: ID={row[0]}, 法律名称={row[1][:30]}..., 内容长度={len(str(row[2]))}")
                
                if i >= 4:  # 只显示前5行
                    break
                    
    except Exception as e:
        print(f"SQL查询测试失败: {e}")


def main():
    """主测试函数"""
    print("开始测试自定义主键功能...")
    print("=" * 60)
    
    test_id_extraction()
    test_custom_pk_implementation()
    
    print("\n" + "=" * 60)
    print("测试完成！")


if __name__ == "__main__":
    main()
