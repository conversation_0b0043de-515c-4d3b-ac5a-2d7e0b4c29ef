import os

from dotenv import load_dotenv

from llm.minstone_llm import LLMFactory

load_dotenv()

factory = LLMFactory().get_instance()
# 大模型
llm = factory.get_chat_model()
# 法律法规的向量存储
law_store = factory.get_law_vector_store()
# 案件的向量存储
case_store = factory.get_case_vector_store()

# 数据库连接信息
db_user = os.getenv("DB_USER")
db_password = os.getenv("DB_PASSWORD")#'minstone'
db_host = os.getenv("DB_HOST")#'************'
db_port = os.getenv("DB_PORT")#'3306'
db_name = os.getenv("DB_NAME")#'ale'

db_connection_string = f'mysql+pymysql://{db_user}:{db_password}@{db_host}:{db_port}/{db_name}'
