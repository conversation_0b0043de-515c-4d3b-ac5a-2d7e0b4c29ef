import re

from langchain_core.output_parsers import StrOutputParser
from langchain_core.prompts import ChatPromptTemplate

from chains import llm

prompt = ChatPromptTemplate.from_messages([
    ("system", "你是一位行政执法专家，负责识别用户意图。请将意图分类为：法律咨询、案例推荐、数据查询或其他。"),
    ("system", "不需要输出<think></部分的内容>"),
    ("human", "用户问题：{input}\n请只回答意图类型（法律咨询/案例推荐/数据查询/其他）：")
])
intend_chain = prompt | llm | StrOutputParser()


def recognize_intent(input: str) -> dict[str, str]:
    intent = intend_chain.invoke({"input": input})
    intent = re.sub(r'<think>.*?</think>', '', intent, flags=re.DOTALL)
    if "法律咨询" in intent:
        return {"intent": "legal_consultant"}
    elif "案例推荐" in intent or "类案" in intent:
        return {"intent": "case_recommender"}
    elif "数据查询" in intent or "查询" in intent or "统计" in intent:
        return {"intent": "data_querier"}
    else:
        return {"intent": "general_responder"}


if __name__ == "__main__":
    response = intend_chain.invoke({"input": "查询案件量"})
    print(response)
