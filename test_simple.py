#!/usr/bin/env python3
"""
简单测试脚本
"""

try:
    print("开始导入模块...")
    
    from langchain_community.utilities import SQLDatabase
    print("✓ SQLDatabase 导入成功")
    
    from datarw.db_datasource_processor import create_enhanced_documents
    print("✓ create_enhanced_documents 导入成功")
    
    from llm.minstone_llm import LLMFactory
    print("✓ LLMFactory 导入成功")
    
    print("所有模块导入成功！")
    
    # 测试数据库连接
    print("\n测试数据库连接...")
    db = SQLDatabase.from_uri("mysql+pymysql://root:minstone123@192.168.2.154:3306/law_item_sc")
    print("✓ 数据库连接成功")
    
    # 测试向量存储
    print("\n测试向量存储...")
    vector_store = LLMFactory.get_instance().get_law_vector_store()
    print(f"✓ 向量存储创建成功: {type(vector_store)}")
    
    print("\n基础测试完成！")
    
except Exception as e:
    print(f"✗ 错误: {e}")
    import traceback
    print(f"详细错误信息: {traceback.format_exc()}")
