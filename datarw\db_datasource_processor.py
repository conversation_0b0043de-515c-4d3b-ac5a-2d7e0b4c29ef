from langchain_community.document_loaders import SQLDatabaseLoader
from langchain_community.utilities import SQLDatabase
from langchain_core.documents import Document
from typing import List, Dict, Any
import logging
from sqlalchemy import text
import json
from decimal import Decimal
from datetime import datetime

from llm.minstone_llm import LLMFactory

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class ProcessorConfig:
    """
    数据库数据源处理器配置类
    """
    # Milvus 相关配置
    MILVUS_DYNAMIC_FIELD_MAX_SIZE = 65536  # 动态字段大小限制（字节）

    # 批处理配置
    DEFAULT_BATCH_SIZE = 100  # 默认批处理大小

    # 数据库配置
    DATABASE_URI = "mysql+pymysql://root:minstone123@192.168.2.154:3306/law_item_sc"
    BUSINESS_CODE = '25'  # 业务编码
    CONTENT_FOLDER_TYPE_THRESHOLD = 3  # 内容文件夹类型阈值

    # 字段截断配置
    TRUNCATION_SUFFIX = "...[截断]"
    TRUNCATION_BUFFER_SIZE = 10  # 截断时保留的缓冲区大小
    LIST_TRUNCATION_MESSAGE = "...[更多项目被截断]"
    DICT_TRUNCATION_KEY = "_truncated"
    DICT_TRUNCATION_MESSAGE = "更多字段被截断"

    # 日志配置
    LOG_LEVEL = logging.INFO


# 向后兼容的常量
MILVUS_DYNAMIC_FIELD_MAX_SIZE = ProcessorConfig.MILVUS_DYNAMIC_FIELD_MAX_SIZE


class QueryBuilder:
    """
    SQL查询构建器
    """

    @staticmethod
    def get_content_ids_query() -> str:
        """获取内容ID的查询"""
        return f"""
        SELECT s.CONTENT_ID
        FROM appr_matter_struct_law s
        WHERE EXISTS (
            SELECT 1
            FROM appr_matter_base_info b
            WHERE b.id = s.FOLDER_ID
                AND b.BUSINESSCODES = '{ProcessorConfig.BUSINESS_CODE}'
        );
        """

    @staticmethod
    def get_matter_info_query(id_list_str: str) -> str:
        """获取事项信息的查询"""
        return f"""
        SELECT DISTINCT ambi.ID, ambi.TASK_NAME, ambi.BUSINESSCODES, amsl.CONTENT_ID
        FROM appr_matter_base_info ambi
        JOIN appr_matter_struct_law amsl ON ambi.ID = amsl.folder_id
        WHERE amsl.CONTENT_ID IN ({id_list_str})
        """

    @staticmethod
    def get_law_detail_query() -> str:
        """获取法律详情的查询"""
        return f"""
        SELECT DISTINCT
            d.ID AS id,
            b.LAW_NAME AS law_name,
            d.CONTENT_INFO AS content_info,
            CAST(SUBSTRING(d.global_sort, 7, 3) AS UNSIGNED) AS 条,
            CAST(SUBSTRING(d.global_sort, 10, 2) AS UNSIGNED) AS 款,
            CAST(SUBSTRING(d.global_sort, 12, 2) AS UNSIGNED) AS 项,
            CAST(SUBSTRING(d.global_sort, 14, 2) AS UNSIGNED) AS 目,
            d.LAW_ID ,
            d.global_sort
        FROM appr_law_detail_info d
        JOIN appr_law_base_info b ON d.LAW_ID = b.ID
        JOIN appr_matter_struct_law s ON d.ID = s.CONTENT_ID
        JOIN appr_matter_base_info m ON s.FOLDER_ID = m.id
        WHERE m.BUSINESSCODES = '{ProcessorConfig.BUSINESS_CODE}'
        AND d.CONTENT_FOLDER_TYPE > {ProcessorConfig.CONTENT_FOLDER_TYPE_THRESHOLD}
        ORDER BY d.LAW_ID, d.global_sort;
        """


def get_json_size(obj):
    """
    计算对象序列化为JSON后的字节大小
    """
    try:
        return len(json.dumps(obj, ensure_ascii=False).encode('utf-8'))
    except Exception:
        return 0


def truncate_large_fields(obj, max_size=ProcessorConfig.MILVUS_DYNAMIC_FIELD_MAX_SIZE):
    """
    截断过大的字段以符合Milvus动态字段大小限制
    """
    if obj is None:
        return obj

    # 如果是字符串，直接检查大小
    if isinstance(obj, str):
        encoded = obj.encode('utf-8')
        if len(encoded) > max_size:
            # 截断字符串，保留前面部分并添加省略号
            buffer_size = ProcessorConfig.TRUNCATION_BUFFER_SIZE
            truncated = encoded[:max_size-buffer_size].decode('utf-8', errors='ignore')
            return truncated + ProcessorConfig.TRUNCATION_SUFFIX
        return obj

    # 如果是列表，逐个检查元素
    elif isinstance(obj, list):
        result = []
        current_size = 2  # [] 的大小
        for item in obj:
            serialized_item = serialize_for_milvus(item)
            item_size = get_json_size(serialized_item)

            if current_size + item_size > max_size:
                # 如果添加这个元素会超过限制，停止添加
                result.append(ProcessorConfig.LIST_TRUNCATION_MESSAGE)
                break

            result.append(serialized_item)
            current_size += item_size + 1  # +1 for comma

        return result

    # 如果是字典，逐个检查键值对
    elif isinstance(obj, dict):
        result = {}
        current_size = 2  # {} 的大小

        for key, value in obj.items():
            serialized_key = str(key)
            serialized_value = serialize_for_milvus(value)

            # 计算这个键值对的大小
            pair_size = get_json_size({serialized_key: serialized_value})

            if current_size + pair_size > max_size:
                # 如果添加这个键值对会超过限制，停止添加
                result[ProcessorConfig.DICT_TRUNCATION_KEY] = ProcessorConfig.DICT_TRUNCATION_MESSAGE
                break

            result[serialized_key] = serialized_value
            current_size += pair_size

        return result

    return obj


def serialize_for_milvus(obj):
    """
    将Python对象序列化为Milvus兼容的JSON格式，并处理大小限制
    """
    if obj is None:
        return None
    elif isinstance(obj, Decimal):
        return float(obj)
    elif isinstance(obj, datetime):
        return obj.isoformat()
    elif isinstance(obj, (str, int, float, bool)):
        return obj
    elif isinstance(obj, list):
        return [serialize_for_milvus(item) for item in obj]
    elif isinstance(obj, dict):
        return {str(key): serialize_for_milvus(value) for key, value in obj.items()}
    elif isinstance(obj, tuple):
        return [serialize_for_milvus(item) for item in obj]
    elif hasattr(obj, '__dict__'):
        # 对于有__dict__属性的对象，转换为字典
        return serialize_for_milvus(obj.__dict__)
    else:
        # 对于其他类型，尝试转换为字符串
        try:
            return str(obj)
        except Exception as e:
            logger.warning(f"无法序列化对象 {type(obj)}: {e}")
            return f"<无法序列化的对象: {type(obj).__name__}>"


def _parse_query_result_to_ids(result) -> List[str]:
    """
    解析数据库查询结果，提取ID列表

    Args:
        result: 数据库查询结果，可能是字符串、列表或其他格式

    Returns:
        List[str]: 解析出的ID列表
    """
    content_ids = []

    if isinstance(result, str):
        content_ids = _parse_string_result(result)
    else:
        content_ids = _parse_structured_result(result)

    return content_ids


def _parse_string_result(result: str) -> List[str]:
    """
    解析字符串格式的查询结果

    Args:
        result: 字符串格式的查询结果

    Returns:
        List[str]: 解析出的ID列表
    """
    import ast
    content_ids = []

    try:
        parsed_result = ast.literal_eval(result)
        logger.debug(f"解析后的结果: {parsed_result}")

        if isinstance(parsed_result, list):
            for row in parsed_result:
                logger.debug(f"处理行: {row}")
                extracted_id = _extract_id_from_row(row)
                if extracted_id:
                    content_ids.append(extracted_id)

    except (ValueError, SyntaxError) as e:
        logger.error(f"解析查询结果失败: {e}")

    return content_ids


def _parse_structured_result(result) -> List[str]:
    """
    解析结构化格式的查询结果

    Args:
        result: 结构化格式的查询结果（列表、元组等）

    Returns:
        List[str]: 解析出的ID列表
    """
    content_ids = []

    for row in result:
        logger.debug(f"处理行: {row}")
        extracted_id = _extract_id_from_row(row)
        if extracted_id:
            content_ids.append(extracted_id)

    return content_ids


def _extract_id_from_row(row) -> str:
    """
    从单行数据中提取ID

    Args:
        row: 单行数据，可能是元组、字典或字符串

    Returns:
        str: 提取的ID，如果无法提取则返回None
    """
    if isinstance(row, tuple) and len(row) > 0:
        return str(row[0])
    elif isinstance(row, dict) and 'CONTENT_ID' in row:
        return str(row['CONTENT_ID'])
    elif isinstance(row, str):
        return row
    else:
        logger.warning(f"无法从行数据中提取ID: {row}")
        return None


def get_content_ids_from_db(db: SQLDatabase) -> List[str]:
    """
    获取现有查询中的 CONTENT_ID 列表
    """
    try:
        id_query = QueryBuilder.get_content_ids_query()
        result = db.run(id_query)
        logger.debug(f"查询结果类型: {type(result)}")

        content_ids = _parse_query_result_to_ids(result)

        logger.info(f"获取到 {len(content_ids)} 个 CONTENT_ID")
        return content_ids

    except Exception as e:
        logger.error(f"获取 CONTENT_ID 失败: {e}")
        return []


def get_matter_info_by_content_ids(db: SQLDatabase, content_ids: List[str], batch_size: int = 1000) -> Dict[str, List[Dict[str, Any]]]:
    """
    根据 CONTENT_ID 列表分批次查询事项信息

    Args:
        db: 数据库连接
        content_ids: CONTENT_ID 列表
        batch_size: 每批次查询的最大 ID 数量

    Returns:
        字典，key 为 CONTENT_ID，value 为对应的事项信息列表
    """
    matter_info_map = {}

    # 分批次处理 ID 列表
    for i in range(0, len(content_ids), batch_size):
        batch_ids = content_ids[i:i + batch_size]
        # 为字符串类型的ID添加引号
        id_list_str = ','.join([f"'{id_}'" for id_ in batch_ids])

        matter_query = QueryBuilder.get_matter_info_query(id_list_str)

        try:
            logger.info(f"执行批次查询，ID 范围: {i+1}-{min(i+batch_size, len(content_ids))}")

            # 使用SQLAlchemy的原始连接执行查询，避免LangChain的字符串转换问题
            with db._engine.connect() as connection:
                query_text = text(matter_query)
                result = connection.execute(query_text)

                # 直接处理查询结果
                for row in result:
                    # SQLAlchemy返回的是Row对象，可以像tuple一样访问
                    content_id = str(row[-1])  # 最后一列是 CONTENT_ID
                    # 修改为新的格式：{"id": 事项ID, "name": 事项TASK_NAME, "businesscodes": 业务编码}
                    matter_info = {
                        'id': serialize_for_milvus(row[0]),  # 事项ID
                        'name': serialize_for_milvus(row[1]),  # 事项TASK_NAME
                        'businesscodes': serialize_for_milvus(row[2])  # 业务编码
                    }

                    if content_id not in matter_info_map:
                        matter_info_map[content_id] = []
                    matter_info_map[content_id].append(matter_info)

        except Exception as e:
            logger.error(f"批次查询失败 (批次 {i//batch_size + 1}): {e}")
            continue

    logger.info(f"成功获取 {len(matter_info_map)} 个 CONTENT_ID 的事项信息")
    return matter_info_map


# SQL查询常量（使用查询构建器）
LAW_DETAIL_QUERY = QueryBuilder.get_law_detail_query()


def _create_enhanced_metadata_mapper(matter_info_map: Dict[str, List[Dict[str, Any]]]):
    """
    创建增强的元数据映射器

    Args:
        matter_info_map: 事项信息映射字典

    Returns:
        callable: 元数据映射器函数
    """
    def enhanced_metadata_mapper(row):
        content_id = row.get("id")
        base_metadata = {
            "法律名称": row.get("law_name"),
            "条": row.get("条"),
            "款": row.get("款"),
            "项": row.get("项"),
            "目": row.get("目"),
            "法律标识": content_id
        }

        # 添加事项列表信息
        base_metadata["事项列表"] = _process_matter_list(content_id, matter_info_map)

        return base_metadata

    return enhanced_metadata_mapper


def _process_matter_list(content_id: str, matter_info_map: Dict[str, List[Dict[str, Any]]]) -> List[Dict[str, Any]]:
    """
    处理事项列表数据

    Args:
        content_id: 内容ID
        matter_info_map: 事项信息映射字典

    Returns:
        List[Dict[str, Any]]: 处理后的事项列表
    """
    if not content_id or content_id not in matter_info_map:
        return []

    try:
        # 序列化事项列表数据以确保Milvus兼容性
        serialized_matter_list = serialize_for_milvus(matter_info_map[content_id])

        # 检查序列化后的数据大小
        json_size = get_json_size(serialized_matter_list)
        if json_size > MILVUS_DYNAMIC_FIELD_MAX_SIZE:
            logger.warning(f"事项列表数据过大 (content_id: {content_id}, 大小: {json_size} 字节)，正在截断...")
            serialized_matter_list = truncate_large_fields(serialized_matter_list)
            logger.info(f"截断后大小: {get_json_size(serialized_matter_list)} 字节")

        logger.debug(f"成功序列化 content_id {content_id} 的事项列表")
        return serialized_matter_list

    except Exception as e:
        logger.error(f"序列化事项列表失败 (content_id: {content_id}): {e}")
        return []


def _extract_document_ids(db: SQLDatabase, docs: List[Document], query: str) -> List[str]:
    """
    提取文档对应的ID列表

    Args:
        db: 数据库连接
        docs: 文档列表
        query: SQL查询语句

    Returns:
        List[str]: 文档ID列表
    """
    # 重新执行查询以获取完整的行数据
    with db._engine.connect() as connection:
        query_text = text(query)
        result = connection.execute(query_text)
        all_rows = list(result)

    logger.info(f"查询返回 {len(all_rows)} 行数据，文档数量 {len(docs)}")

    # 确保文档和ID数量匹配
    if len(docs) == len(all_rows):
        # 数量匹配，直接使用查询结果的ID
        doc_ids = [str(row[0]) for row in all_rows]
        logger.info(f"成功匹配 {len(doc_ids)} 个文档ID")
        return doc_ids
    else:
        # 数量不匹配，使用备用方案
        return _generate_fallback_document_ids(docs)


def _generate_fallback_document_ids(docs: List[Document]) -> List[str]:
    """
    生成备用文档ID列表

    Args:
        docs: 文档列表

    Returns:
        List[str]: 备用文档ID列表
    """
    logger.warning(f"文档数量与查询结果数量不匹配，使用备用方案")
    logger.warning("可能原因：SQLDatabaseLoader内部过滤了某些记录")

    doc_ids = []
    # 尝试从文档的metadata中提取ID（如果可能）
    for i, doc in enumerate(docs):
        if "法律标识" in doc.metadata and doc.metadata["法律标识"]:
            doc_ids.append(str(doc.metadata["法律标识"]))
        else:
            doc_ids.append(f"doc_{i}")

    logger.info(f"使用备用方案生成 {len(doc_ids)} 个文档ID")
    return doc_ids


def create_enhanced_documents(db: SQLDatabase) -> tuple[List[Document], List[str]]:
    """
    创建增强的文档，包含事项列表信息
    返回文档列表和对应的ID列表
    """
    # 1. 获取 CONTENT_ID 列表
    content_ids = get_content_ids_from_db(db)
    if not content_ids:
        logger.warning("未获取到任何 CONTENT_ID，使用原始查询")
        return create_original_documents(db)

    # 2. 分批次查询事项信息
    matter_info_map = get_matter_info_by_content_ids(db, content_ids)

    # 3. 创建增强的文档加载器
    metadata_mapper = _create_enhanced_metadata_mapper(matter_info_map)

    loader = SQLDatabaseLoader(
        db=db,
        query=LAW_DETAIL_QUERY,
        page_content_mapper=lambda row: row.get("content_info"),
        metadata_mapper=metadata_mapper
    )

    docs = loader.load()

    # 4. 提取文档对应的ID列表
    doc_ids = _extract_document_ids(db, docs, LAW_DETAIL_QUERY)

    return docs, doc_ids


def create_original_documents(db: SQLDatabase) -> tuple[List[Document], List[str]]:
    """
    创建原始文档（备用方案）
    返回文档列表和对应的ID列表
    """
    sql = QueryBuilder.get_law_detail_query()

    loader = SQLDatabaseLoader(
        db=db,
        query=sql,
        page_content_mapper=lambda row: row.get("content_info"),
        metadata_mapper=lambda row: {
            "法律名称": row.get("law_name"),
            "条": row.get("条"),
            "款": row.get("款"),
            "项": row.get("项"),
            "目": row.get("目"),
            "法律标识": row.get("id"),
            "事项列表": []  # 空的事项列表
        }
    )

    docs = loader.load()

    # 提取文档对应的ID列表
    # 从已加载的文档中提取ID，确保数量完全匹配
    doc_ids = []

    # 重新执行查询以获取完整的行数据
    with db._engine.connect() as connection:
        query_text = text(sql)
        result = connection.execute(query_text)
        all_rows = list(result)

    logger.info(f"查询返回 {len(all_rows)} 行数据，文档数量 {len(docs)}（原始方案）")

    # 确保文档和ID数量匹配
    if len(docs) == len(all_rows):
        # 数量匹配，直接使用查询结果的ID
        doc_ids = [str(row[0]) for row in all_rows]
        logger.info(f"成功匹配 {len(doc_ids)} 个文档ID（原始方案）")
    else:
        # 数量不匹配，使用文档索引作为备用ID，并记录详细信息
        logger.warning(f"文档数量 ({len(docs)}) 与查询结果数量 ({len(all_rows)}) 不匹配（原始方案）")
        logger.warning("可能原因：SQLDatabaseLoader内部过滤了某些记录")

        # 尝试从文档的metadata中提取ID（如果可能）
        for i, doc in enumerate(docs):
            if "法律标识" in doc.metadata and doc.metadata["法律标识"]:
                doc_ids.append(str(doc.metadata["法律标识"]))
            else:
                doc_ids.append(f"doc_{i}")

        logger.info(f"使用备用方案生成 {len(doc_ids)} 个文档ID（原始方案）")

    return docs, doc_ids


class DocumentProcessor:
    """
    文档处理器，负责文档的创建、处理和向量存储添加
    """

    def __init__(self, db: SQLDatabase, batch_size: int = 100):
        self.db = db
        self.batch_size = batch_size
        self.batch_processor = None

    def process_and_store_documents(self) -> bool:
        """
        处理文档并存储到向量存储

        Returns:
            bool: 处理是否成功
        """
        try:
            # 创建文档
            docs, doc_ids = self._create_documents()
            logger.info(f"成功创建 {len(docs)} 个文档，对应 {len(doc_ids)} 个ID")

            # 获取向量存储
            vector_store = LLMFactory.get_instance().get_law_vector_store()

            # 初始化批处理器
            self.batch_processor = DocumentBatchProcessor(vector_store, self.batch_size)

            # 处理文档
            success = self.batch_processor.process_documents(docs, doc_ids)

            if success:
                logger.info("文档已成功添加到向量存储")
                return True
            else:
                raise Exception("批处理失败")

        except Exception as e:
            logger.error(f"文档处理过程中发生错误: {e}")
            import traceback
            logger.error(f"详细错误信息: {traceback.format_exc()}")

            # 使用原始方法作为备用
            return self._fallback_to_original_method()

    def _create_documents(self) -> tuple[List[Document], List[str]]:
        """
        创建文档，优先使用增强方法，失败时使用原始方法
        """
        try:
            return create_enhanced_documents(self.db)
        except Exception as e:
            logger.warning(f"增强文档创建失败: {e}，使用原始方法")
            return create_original_documents(self.db)

    def _fallback_to_original_method(self) -> bool:
        """
        备用方法：使用原始文档创建方法
        """
        logger.info("尝试使用原始方法...")
        try:
            docs, doc_ids = create_original_documents(self.db)
            vector_store = LLMFactory.get_instance().get_law_vector_store()
            vector_store.add_documents(docs, ids=doc_ids)
            logger.info("使用原始方法成功添加文档到向量存储")
            return True
        except Exception as fallback_error:
            logger.error(f"原始方法也失败了: {fallback_error}")
            return False


class DocumentBatchProcessor:
    """
    文档批处理器，负责处理文档的批量添加到向量存储
    """

    def __init__(self, vector_store, batch_size: int = 100):
        self.vector_store = vector_store
        self.batch_size = batch_size

    def process_documents(self, docs: List[Document], doc_ids: List[str]) -> bool:
        """
        批量处理文档添加到向量存储

        Args:
            docs: 文档列表
            doc_ids: 文档ID列表

        Returns:
            bool: 处理是否成功
        """
        try:
            logger.info(f"开始批量处理 {len(docs)} 个文档")

            for i in range(0, len(docs), self.batch_size):
                batch_docs = docs[i:i + self.batch_size]
                batch_ids = doc_ids[i:i + self.batch_size]

                # 验证并调整批次数据
                batch_docs, batch_ids = self._validate_and_adjust_batch(batch_docs, batch_ids, i)

                # 处理当前批次
                self._process_batch(batch_docs, batch_ids, i)

            logger.info("文档批量处理完成")
            return True

        except Exception as e:
            logger.error(f"文档批量处理失败: {e}")
            return False

    def _validate_and_adjust_batch(self, batch_docs: List[Document], batch_ids: List[str], batch_start_index: int) -> tuple[List[Document], List[str]]:
        """
        验证并调整批次中的文档和ID数量匹配
        """
        batch_num = batch_start_index // self.batch_size + 1

        if len(batch_docs) != len(batch_ids):
            logger.error(f"批次 {batch_num} 中文档数量 ({len(batch_docs)}) 与ID数量 ({len(batch_ids)}) 不匹配")

            # 调整ID列表长度以匹配文档数量
            if len(batch_docs) < len(batch_ids):
                batch_ids = batch_ids[:len(batch_docs)]
            else:
                # 如果文档多于ID，为多出的文档生成备用ID
                for j in range(len(batch_ids), len(batch_docs)):
                    batch_ids.append(f"doc_{batch_start_index + j}")

            logger.info(f"已调整批次 {batch_num} 的ID列表，现在有 {len(batch_ids)} 个ID")

        return batch_docs, batch_ids

    def _process_batch(self, batch_docs: List[Document], batch_ids: List[str], batch_start_index: int):
        """
        处理单个批次的文档
        """
        batch_num = batch_start_index // self.batch_size + 1
        total_batches = (len(batch_docs) + self.batch_size - 1) // self.batch_size

        try:
            self.vector_store.add_documents(batch_docs, ids=batch_ids)
            logger.info(f"成功添加批次 {batch_num}/{total_batches}")
        except Exception as batch_error:
            logger.error(f"批次 {batch_num} 添加失败: {batch_error}")
            # 尝试逐个添加以找出问题文档
            self._process_documents_individually(batch_docs, batch_ids)

    def _process_documents_individually(self, batch_docs: List[Document], batch_ids: List[str]):
        """
        逐个处理文档，用于批次失败时的错误恢复
        """
        for doc, doc_id in zip(batch_docs, batch_ids):
            try:
                processed_doc = self._process_single_document(doc, doc_id)
                self.vector_store.add_documents([processed_doc], ids=[doc_id])
                logger.debug(f"成功添加文档 {doc_id}")
            except Exception as doc_error:
                logger.error(f"文档 {doc_id} 添加失败: {doc_error}")
                self._handle_problematic_document(doc, doc_id)

    def _process_single_document(self, doc: Document, doc_id: str) -> Document:
        """
        处理单个文档，检查并处理元数据大小问题
        """
        metadata_size = get_json_size(doc.metadata)

        if metadata_size > MILVUS_DYNAMIC_FIELD_MAX_SIZE:
            logger.warning(f"文档 {doc_id} 元数据过大 ({metadata_size} 字节)，正在处理...")

            # 处理过大的元数据
            processed_metadata = {}
            for key, value in doc.metadata.items():
                if key == "事项列表":
                    processed_metadata[key] = truncate_large_fields(value)
                else:
                    processed_metadata[key] = value

            return Document(page_content=doc.page_content, metadata=processed_metadata)

        return doc

    def _handle_problematic_document(self, doc: Document, doc_id: str):
        """
        处理有问题的文档，使用最小元数据重试
        """
        logger.error(f"问题文档元数据大小: {get_json_size(doc.metadata)} 字节")

        try:
            logger.info(f"尝试进一步处理文档 {doc_id}...")
            minimal_metadata = {
                "法律名称": doc.metadata.get("法律名称", ""),
                "条": doc.metadata.get("条", 0),
                "款": doc.metadata.get("款", 0),
                "项": doc.metadata.get("项", 0),
                "目": doc.metadata.get("目", 0),
                "法律标识": doc.metadata.get("法律标识", doc_id),
                "事项列表": []  # 清空事项列表
            }

            minimal_doc = Document(page_content=doc.page_content, metadata=minimal_metadata)
            self.vector_store.add_documents([minimal_doc], ids=[doc_id])
            logger.info(f"使用最小元数据成功添加文档 {doc_id}")
        except Exception as minimal_error:
            logger.error(f"即使使用最小元数据也无法添加文档 {doc_id}: {minimal_error}")


def main():
    """
    主函数，处理文档创建和向量存储添加
    """
    db = SQLDatabase.from_uri(ProcessorConfig.DATABASE_URI)

    # 使用文档处理器处理所有逻辑
    processor = DocumentProcessor(db, batch_size=100)
    success = processor.process_and_store_documents()

    if not success:
        logger.error("文档处理失败")
        exit(1)
    else:
        logger.info("文档处理完成")


if __name__ == "__main__":
    main()
