from langchain_core.output_parsers import StrOutputParser

from chains import law_store, llm
from langchain_core.prompts import ChatPromptTemplate

# 生成专业回答
prompt = ChatPromptTemplate.from_messages([
    ("system", "你是一位行政执法专家，请基于以下法律条文回答用户问题：\n{legal_context}"),
    ("human", "用户问题：{input}"),
    ("system","""
请按照以下格式输出：
1. 违法事实认定的法律条款
2. 根据当事人违法行为作出处罚定义的法律条款
3. 执法程序提示
4. 结论

要求：
- 优先推荐最新颁布的法律
- 排除已废止的条款
- 结合案件实际情况分析
- 引用完整的法律条款
""")
])
legal_chain = prompt | llm | StrOutputParser()

def legal_consultant(input: str) -> dict[str, str]:
    retriever = law_store.as_retriever(search_kwargs={"k": 5})
    docs = retriever.invoke(input)
    legal_context = [{"content": doc.page_content} for doc in docs]
    response = legal_chain.invoke({"input": input,
                                   "legal_context": "\n\n".join([doc["content"] for doc in legal_context])
                                   })
    return {
        "legal_context": legal_context,
        "output": f"【法律咨询结果】\n{response}"
    }
