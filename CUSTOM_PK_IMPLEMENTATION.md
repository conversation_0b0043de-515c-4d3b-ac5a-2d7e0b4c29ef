# 实现将 original_sql 查询出来的 id 作为向量数据库的 pk 字段

## 概述

本文档描述了如何修改现有代码，使得从 `original_sql` 查询出来的 `id` 字段作为 Milvus 向量数据库的主键（pk）字段，而不是使用自动生成的 ID。

## 主要修改

### 1. 修改向量存储配置 (`llm/minstone_llm.py`)

**修改前：**
```python
def get_law_vector_store(self):
    return Milvus(embedding_function=self.get_embedding_model(), auto_id=True,
                  enable_dynamic_field=True,
                  collection_name="laws",
                  connection_args={
                      "uri": os.getenv("MILVUS_URL")
                  })
```

**修改后：**
```python
def get_law_vector_store(self):
    return Milvus(embedding_function=self.get_embedding_model(), auto_id=False,
                  enable_dynamic_field=True,
                  collection_name="laws",
                  connection_args={
                      "uri": os.getenv("MILVUS_URL")
                  })
```

**关键变化：** `auto_id=True` 改为 `auto_id=False`，这样就可以使用自定义 ID 而不是自动生成的 ID。

### 2. 修改文档创建函数 (`datarw/db_datasource_processor.py`)

#### 2.1 修改 `create_enhanced_documents` 函数

**修改前：**
```python
def create_enhanced_documents(db: SQLDatabase) -> List[Document]:
```

**修改后：**
```python
def create_enhanced_documents(db: SQLDatabase) -> tuple[List[Document], List[str]]:
```

**关键变化：**
- 函数现在返回一个元组：`(文档列表, ID列表)`
- 在函数末尾添加了 ID 提取逻辑：

```python
# 5. 提取文档对应的ID列表
# 重新执行查询以获取ID列表，确保顺序一致
with db._engine.connect() as connection:
    query_text = text(original_sql)
    result = connection.execute(query_text)
    doc_ids = [str(row[0]) for row in result]  # 第一列是 ID

logger.info(f"提取到 {len(doc_ids)} 个文档ID")

if len(docs) != len(doc_ids):
    logger.warning(f"文档数量 ({len(docs)}) 与ID数量 ({len(doc_ids)}) 不匹配")
    # 如果数量不匹配，使用文档索引作为备用ID
    doc_ids = [f"doc_{i}" for i in range(len(docs))]

return docs, doc_ids
```

#### 2.2 修改 `create_original_documents` 函数

**修改前：**
```python
def create_original_documents(db: SQLDatabase) -> List[Document]:
```

**修改后：**
```python
def create_original_documents(db: SQLDatabase) -> tuple[List[Document], List[str]]:
```

**关键变化：** 同样添加了 ID 提取逻辑，确保备用方案也支持自定义 ID。

### 3. 修改文档插入逻辑

**修改前：**
```python
docs = create_enhanced_documents(db)
vector_store.add_documents(batch_docs)
```

**修改后：**
```python
docs, doc_ids = create_enhanced_documents(db)
vector_store.add_documents(batch_docs, ids=batch_ids)
```

**关键变化：**
- 解包函数返回值以获取文档和对应的 ID
- 在 `add_documents` 调用中传递 `ids` 参数
- 在批处理和错误处理中都使用自定义 ID

### 4. 修改 `db_datasource_processor2.py`

为了保持一致性，也修改了简化版本的处理器：

```python
# 提取文档对应的ID列表
from sqlalchemy import text
with db._engine.connect() as connection:
    query_text = text(sql)
    result = connection.execute(query_text)
    doc_ids = [str(row[0]) for row in result]  # 第一列是 ID

print(f"提取到 {len(doc_ids)} 个文档ID")

if len(docs) != len(doc_ids):
    print(f"警告：文档数量 ({len(docs)}) 与ID数量 ({len(doc_ids)}) 不匹配")
    # 如果数量不匹配，使用文档索引作为备用ID
    doc_ids = [f"doc_{i}" for i in range(len(docs))]

vector_store = LLMFactory.get_instance().get_law_vector_store()
vector_store.add_documents(docs, ids=doc_ids)
```

## 实现原理

### 1. ID 提取机制

从 SQL 查询结果中提取 ID 的关键是确保：
- SQL 查询的第一列是 `d.ID AS id`
- 使用 SQLAlchemy 的原始连接执行相同的查询
- 提取每行的第一列作为文档 ID
- 将 ID 转换为字符串格式

### 2. 顺序一致性

为了确保文档和 ID 的对应关系正确：
- 使用相同的 SQL 查询和排序条件
- 按相同顺序处理文档和 ID
- 在数量不匹配时提供备用方案

### 3. 错误处理

实现了多层错误处理：
- 如果 ID 数量与文档数量不匹配，使用索引作为备用 ID
- 在批处理失败时，逐个处理文档
- 在元数据过大时，进行截断处理
- 提供原始方法作为最终备用方案

## 使用方法

### 运行增强版处理器

```bash
python datarw/db_datasource_processor.py
```

### 运行简化版处理器

```bash
python datarw/db_datasource_processor2.py
```

### 测试实现

```bash
python test_custom_pk.py
```

## 注意事项

1. **Milvus 配置**：确保 Milvus 服务正常运行，并且连接配置正确
2. **数据库连接**：确保数据库连接字符串正确
3. **ID 唯一性**：确保从 SQL 查询提取的 ID 是唯一的
4. **数据类型**：ID 会被转换为字符串类型存储在 Milvus 中
5. **性能考虑**：大批量数据时建议使用批处理方式

## 优势

1. **可追溯性**：向量数据库中的文档可以直接通过原始数据库的 ID 进行关联
2. **一致性**：确保向量数据库和关系数据库之间的数据一致性
3. **查询效率**：可以通过已知的 ID 直接查询特定文档
4. **数据管理**：便于数据更新、删除等管理操作

## 测试验证

创建了测试脚本 `test_custom_pk.py` 来验证：
- ID 提取功能
- 文档创建功能
- 向量存储配置
- 文档插入功能
- 查询功能

通过这些修改，现在向量数据库使用的是从 `original_sql` 查询出来的实际 ID，而不是自动生成的 ID，实现了您的需求。
