from langchain_core.output_parsers import StrOutputParser

from chains import case_store, llm
from langchain_core.prompts import ChatPromptTemplate

# 生成推荐理由
prompt = ChatPromptTemplate.from_messages([
    ("system", "你是一位行政执法专家，请基于以下相似案例回答用户问题：\n{case_context}"),
    ("human", "用户问题：{input}\n请推荐最相关的3个案例，并说明理由："),
    ("system", "要求包括案件的完整内容")
])

case_recommender_chain = prompt | llm | StrOutputParser()


def case_recommender(input: str) -> dict[str, str]:
    retriever = case_store.as_retriever(search_kwargs={"k": 3})
    docs = retriever.invoke(input)
    case_context = [{"content": doc.page_content} for doc in docs]
    response = case_recommender_chain.invoke({
        "input": input,
        "case_context": "\n\n".join([doc["content"] for doc in case_context])
    })

    return {
        "case_context": case_context,
        "output": f"【类案推荐结果】\n{response}"
    }
