#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试NaN值修复功能
验证数据清洗是否能正确处理NaN值，避免Milvus错误
"""

import pandas as pd
import numpy as np
from langchain_core.documents import Document
from case_data_rw import CaseDataLoader


def create_test_data_with_nan():
    """创建包含NaN值的测试数据"""
    test_data = {
        "案号": ["CASE001", "CASE002", "CASE003", "CASE004"],
        "案件简要描述": [
            "正常的案件描述",
            "另一个正常描述", 
            "",  # 空字符串
            "第四个案件描述"
        ],
        "案由": ["合同纠纷", np.nan, "侵权纠纷", "劳动争议"],  # 包含NaN
        "违法事实": [np.nan, "违法事实描述", np.nan, "另一个违法事实"],  # 包含NaN
        "违法依据": ["法律条文1", "法律条文2", np.nan, "法律条文4"],  # 包含NaN
        "处罚依据": ["处罚条文1", np.nan, "处罚条文3", np.nan],  # 包含NaN
        "处罚内容": [np.nan, np.nan, "罚款1000元", "警告"],  # 包含NaN
        "执法事项": ["执法事项1", "执法事项2", np.nan, "执法事项4"],  # 包含NaN
        "事项编码": ["CODE001", "CODE002", "CODE003", np.nan]  # 包含NaN
    }
    
    return pd.DataFrame(test_data)


def test_nan_handling():
    """测试NaN值处理"""
    print("=" * 80)
    print("NaN值处理测试")
    print("=" * 80)
    
    # 创建包含NaN的测试数据
    test_df = create_test_data_with_nan()
    
    print("\n原始测试数据:")
    print("-" * 50)
    print(test_df)
    print(f"\n数据类型:")
    print(test_df.dtypes)
    print(f"\nNaN值统计:")
    print(test_df.isnull().sum())
    
    # 使用高级清洗模式测试
    loader = CaseDataLoader(use_advanced_cleaning=True)
    
    print("\n" + "=" * 80)
    print("开始数据清洗和处理")
    print("=" * 80)
    
    try:
        # 1. 案件描述清洗
        df_desc_cleaned = loader.clean_dataframe_descriptions(test_df.copy(), "test_nan.xlsx")
        
        # 2. 数据质量检查
        df_quality_checked = loader.check_data_quality(df_desc_cleaned, "test_nan.xlsx")
        
        # 3. 元数据清洗
        df_metadata_cleaned = loader.clean_metadata_for_vectorstore(df_quality_checked, "test_nan.xlsx")
        
        print(f"\n清洗后数据:")
        print("-" * 50)
        print(df_metadata_cleaned)
        print(f"\n清洗后数据类型:")
        print(df_metadata_cleaned.dtypes)
        print(f"\n清洗后NaN值统计:")
        print(df_metadata_cleaned.isnull().sum())
        
        # 4. 创建文档
        from langchain_community.document_loaders import DataFrameLoader
        if len(df_metadata_cleaned) > 0:
            loader_df = DataFrameLoader(df_metadata_cleaned, page_content_column="案件简要描述")
            docs = loader_df.load()
            
            print(f"\n创建的文档数量: {len(docs)}")
            
            # 5. 验证文档
            validated_docs = loader.validate_for_vectorstore(docs, "test_nan.xlsx")
            
            print(f"\n验证后的文档数量: {len(validated_docs)}")
            
            # 6. 显示文档内容
            print(f"\n文档内容示例:")
            print("-" * 50)
            for i, doc in enumerate(validated_docs[:2]):  # 只显示前2个
                print(f"文档 {i}:")
                print(f"  page_content: '{doc.page_content}'")
                print(f"  metadata: {doc.metadata}")
                print()
            
            # 7. 测试JSON序列化
            print(f"\nJSON序列化测试:")
            print("-" * 50)
            import json
            for i, doc in enumerate(validated_docs):
                try:
                    json_str = json.dumps(doc.metadata, ensure_ascii=False)
                    print(f"文档 {i}: JSON序列化成功 ✓")
                except Exception as e:
                    print(f"文档 {i}: JSON序列化失败 ✗ - {e}")
            
            return validated_docs
        else:
            print("⚠ 警告: 清洗后没有有效数据")
            return []
            
    except Exception as e:
        print(f"✗ 测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
        return []


def test_writer_safety():
    """测试写入器的安全性"""
    print("\n" + "=" * 80)
    print("写入器安全性测试")
    print("=" * 80)
    
    # 创建一些测试文档，包括有问题的文档
    test_docs = [
        Document(page_content="正常文档", metadata={"key1": "value1", "key2": "value2"}),
        Document(page_content="", metadata={"key1": "empty_content"}),  # 空内容
        Document(page_content="包含NaN的文档", metadata={"key1": np.nan, "key2": "value2"}),  # NaN metadata
        Document(page_content="正常文档2", metadata={"key1": "value1", "key2": None}),  # None metadata
    ]
    
    from case_data_rw import CaseDataWriter
    
    # 注意：这里不会真正写入数据库，只是测试处理逻辑
    print("创建CaseDataWriter实例...")
    writer = CaseDataWriter(test_docs)
    
    print("测试文档预处理...")
    safe_docs = []
    for i, doc in enumerate(test_docs):
        try:
            # 模拟writer中的安全检查逻辑
            if not doc.page_content or doc.page_content.strip() == "":
                print(f"跳过文档 {i}: page_content为空")
                continue
            
            clean_metadata = {}
            for key, value in doc.metadata.items():
                if pd.isnull(value) or str(value).lower() in ['nan', 'none', 'null']:
                    clean_metadata[key] = ""
                else:
                    clean_metadata[key] = str(value)
            
            clean_doc = Document(
                page_content=doc.page_content,
                metadata=clean_metadata
            )
            safe_docs.append(clean_doc)
            print(f"文档 {i}: 处理成功 ✓")
            
        except Exception as e:
            print(f"文档 {i}: 处理失败 ✗ - {e}")
    
    print(f"\n处理结果:")
    print(f"  原始文档数: {len(test_docs)}")
    print(f"  安全文档数: {len(safe_docs)}")
    
    return safe_docs


if __name__ == "__main__":
    # 测试NaN处理
    validated_docs = test_nan_handling()
    
    # 测试写入器安全性
    safe_docs = test_writer_safety()
    
    print(f"\n" + "=" * 80)
    print("测试总结")
    print("=" * 80)
    print(f"NaN处理测试: 生成 {len(validated_docs)} 个有效文档")
    print(f"写入器安全测试: 生成 {len(safe_docs)} 个安全文档")
    print("所有测试完成！")
