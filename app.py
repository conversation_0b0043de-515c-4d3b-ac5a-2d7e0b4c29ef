import sys
from typing import Optional, List, Dict

from langgraph.graph import END, StateGraph

from chains.case_recommender_chain import case_recommender
from chains.general_chain import general
from chains.intend_chain import recognize_intent
from chains.legal_chain import legal_consultant
from chains.nl2sql_chain import run_sql

# 根据Python版本选择正确的TypedDict
if sys.version_info >= (3, 12):
    from typing import TypedDict
else:
    from typing_extensions import TypedDict


class AgentState(TypedDict):
    input: str
    intent: Optional[str]
    legal_context: Optional[List[Dict]]
    case_context: Optional[List[Dict]]
    sql_result: Optional[str]
    output: Optional[str]

# 意图识别
def __recognize_intent(state: AgentState):
    return recognize_intent(state["input"])

# 法律咨询
def __legal_consultant(state: AgentState):
    return legal_consultant(state["input"])

# 类案推荐
def __case_recommender(state: AgentState):
    return case_recommender(state["input"])

# 数据查询
def __data_query(state: AgentState):
    return run_sql(state["input"])

# 其他
def __general(state: AgentState):
    return general(state["input"])

# 5. 最终响应节点
def __final_response(state: AgentState):
    # 这里可以添加最终处理逻辑，如格式化输出
    return {"output": state["output"]}


workflow = StateGraph(AgentState)

# 添加节点
workflow.add_node("intent_recognizer", __recognize_intent)
workflow.add_node("legal_consultant", __legal_consultant)
workflow.add_node("case_recommender", __case_recommender)
workflow.add_node("data_querier", __data_query)
workflow.add_node("general_responder", __general)
workflow.add_node("final_responder", __final_response)

# 设置入口点
workflow.set_entry_point("intent_recognizer")

# 添加条件边
def route_by_intent(state: AgentState):
    return state.get("intent", "general")

workflow.add_conditional_edges(
    "intent_recognizer",
    route_by_intent,
    {
        "legal_consultant": "legal_consultant",
        "case_recommender": "case_recommender",
        "data_querier": "data_querier",
        "general_responder": "general_responder"
    }
)

# 添加常规边
workflow.add_edge("legal_consultant", "final_responder")
workflow.add_edge("case_recommender", "final_responder")
workflow.add_edge("data_querier", "final_responder")
workflow.add_edge("general_responder", "final_responder")
workflow.add_edge("final_responder", END)

# 编译图
agent = workflow.compile()

# 智能体执行函数
def law_agent(query: str) -> str:
    # 获取最终输出
    final_state = agent.invoke({"input": query})
    return final_state["output"]

if __name__ == "__main__":
    print(law_agent("夸大宣传违反法律了吗？"))
