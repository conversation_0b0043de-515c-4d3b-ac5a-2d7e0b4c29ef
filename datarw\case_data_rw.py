import os
import re

import pandas
from dotenv import load_dotenv
from langchain_community.document_loaders import DataFrameLoader
from langchain_core.documents import Document

from llm.minstone_llm import LLMFactory


class CaseDataLoader:
    def __init__(self, use_advanced_cleaning=False):
        load_dotenv()
        self.cases_dir = os.getenv('CASES_DIR')
        self.use_advanced_cleaning = use_advanced_cleaning

    def clean_case_description(self, text: str) -> str:
        """
        清洗案件简要描述文本

        Args:
            text: 原始文本

        Returns:
            清洗后的文本
        """
        if pandas.isnull(text) or text == "":
            return ""

        # 转换为字符串
        text = str(text)

        # 1. 去除首尾空白字符
        text = text.strip()

        # 2. 统一换行符
        text = text.replace('\r\n', '\n').replace('\r', '\n')

        # 3. 去除多余的空白字符（多个空格、制表符等）
        text = re.sub(r'\s+', ' ', text)

        # 4. 去除特殊字符和控制字符（保留中文、英文、数字、常用标点）
        text = re.sub(r'[^\u4e00-\u9fff\u3400-\u4dbf\w\s.,;:!?()（）【】""''、。，；：！？—-]', '', text)

        # 5. 处理重复的标点符号
        text = re.sub(r'[。]{2,}', '。', text)  # 多个句号
        text = re.sub(r'[，]{2,}', '，', text)  # 多个逗号
        text = re.sub(r'[！]{2,}', '！', text)  # 多个感叹号
        text = re.sub(r'[？]{2,}', '？', text)  # 多个问号

        # 6. 去除开头的无意义字符
        text = re.sub(r'^[，。；：、\s]+', '', text)

        # 7. 去除结尾的无意义字符
        text = re.sub(r'[，；：、\s]+$', '', text)

        # 8. 处理常见的错误格式
        # 去除多余的括号内容（如果是空的或只有空格）
        text = re.sub(r'[（(]\s*[）)]', '', text)

        # 9. 最终清理
        text = text.strip()

        return text

    def clean_dataframe_descriptions(self, df: pandas.DataFrame, filename: str) -> pandas.DataFrame:
        """
        对DataFrame中的案件简要描述列进行批量清洗

        Args:
            df: 要清洗的DataFrame
            filename: 文件名，用于日志输出

        Returns:
            清洗后的DataFrame
        """
        if "案件简要描述" not in df.columns:
            return df

        print(f"\n=== 数据清洗: {filename} ===")

        # 记录清洗前的统计信息
        original_descriptions = df["案件简要描述"].copy()

        # 根据配置选择清洗方法
        if self.use_advanced_cleaning:
            print("使用高级数据清洗模式")
            df["案件简要描述"] = df["案件简要描述"].apply(self.advanced_clean_case_description)
        else:
            print("使用标准数据清洗模式")
            df["案件简要描述"] = df["案件简要描述"].apply(self.clean_case_description)

        # 统计清洗效果
        changed_count = 0
        empty_after_clean = 0

        print("清洗前后对比示例:")
        print("-" * 80)

        for idx, (original, cleaned) in enumerate(zip(original_descriptions, df["案件简要描述"])):
            if str(original) != str(cleaned):
                changed_count += 1
                if changed_count <= 5:  # 只显示前5个变化的例子
                    print(f"行 {idx}:")
                    print(f"  原文: '{original}'")
                    print(f"  清洗后: '{cleaned}'")
                    print()

            if cleaned == "":
                empty_after_clean += 1

        print(f"清洗统计:")
        print(f"  总行数: {len(df)}")
        print(f"  发生变化的行数: {changed_count}")
        print(f"  清洗后变为空的行数: {empty_after_clean}")
        print(f"  清洗率: {changed_count/len(df)*100:.1f}%" if len(df) > 0 else "  清洗率: 0%")

        return df

    def clean_metadata_for_vectorstore(self, df: pandas.DataFrame, filename: str) -> pandas.DataFrame:
        """
        清洗DataFrame中的所有列，确保兼容向量数据库
        处理NaN值、特殊字符等问题

        Args:
            df: 要清洗的DataFrame
            filename: 文件名，用于日志输出

        Returns:
            清洗后的DataFrame
        """
        print(f"\n=== 元数据清洗: {filename} ===")

        df_cleaned = df.copy()
        nan_replacements = 0

        # 处理所有列的NaN值和特殊字符
        for column in df_cleaned.columns:
            # 统计该列的NaN数量
            nan_count = df_cleaned[column].isnull().sum()
            if nan_count > 0:
                print(f"列 '{column}': 发现 {nan_count} 个NaN值")
                nan_replacements += nan_count

            # 将NaN值替换为空字符串，并确保所有值都是字符串类型
            df_cleaned[column] = df_cleaned[column].fillna("").astype(str)

            # 处理可能导致JSON解析错误的特殊字符
            if column != "案件简要描述":  # 案件简要描述已经单独清洗过了
                df_cleaned[column] = df_cleaned[column].apply(self._clean_metadata_field)

        print(f"元数据清洗统计:")
        print(f"  处理的列数: {len(df_cleaned.columns)}")
        print(f"  替换的NaN值总数: {nan_replacements}")
        print(f"  清洗后数据类型: 全部转换为字符串")

        return df_cleaned

    def _clean_metadata_field(self, value: str) -> str:
        """
        清洗单个元数据字段

        Args:
            value: 字段值

        Returns:
            清洗后的字段值
        """
        if pandas.isnull(value) or value == "nan" or value == "None":
            return ""

        value = str(value).strip()

        # 去除可能导致JSON解析错误的控制字符
        value = re.sub(r'[\x00-\x1f\x7f-\x9f]', '', value)

        # 处理可能的编码问题
        try:
            # 确保字符串可以正确编码为UTF-8
            value.encode('utf-8')
        except UnicodeEncodeError:
            # 如果有编码问题，移除有问题的字符
            value = value.encode('utf-8', errors='ignore').decode('utf-8')

        return value

    def validate_for_vectorstore(self, docs: list[Document], filename: str) -> list[Document]:
        """
        验证文档是否适合向量数据库存储

        Args:
            docs: 文档列表
            filename: 文件名，用于日志输出

        Returns:
            验证通过的文档列表
        """
        print(f"\n=== 向量数据库兼容性验证: {filename} ===")

        valid_docs = []
        invalid_count = 0

        for i, doc in enumerate(docs):
            try:
                # 检查page_content
                if not doc.page_content or doc.page_content.strip() == "":
                    print(f"文档 {i}: page_content为空，跳过")
                    invalid_count += 1
                    continue

                # 检查metadata中是否有问题
                metadata_valid = True
                for key, value in doc.metadata.items():
                    # 确保所有值都是字符串且不包含NaN
                    if pandas.isnull(value) or str(value).lower() in ['nan', 'none', 'null']:
                        doc.metadata[key] = ""
                    else:
                        doc.metadata[key] = str(value)

                    # 检查是否包含可能导致JSON解析错误的字符
                    try:
                        import json
                        json.dumps({key: doc.metadata[key]})
                    except (TypeError, ValueError) as e:
                        print(f"文档 {i}: metadata字段 '{key}' JSON序列化失败: {e}")
                        doc.metadata[key] = str(doc.metadata[key]).encode('utf-8', errors='ignore').decode('utf-8')

                if metadata_valid:
                    valid_docs.append(doc)
                else:
                    invalid_count += 1

            except Exception as e:
                print(f"文档 {i}: 验证时发生异常: {e}")
                invalid_count += 1
                continue

        print(f"验证结果:")
        print(f"  原始文档数: {len(docs)}")
        print(f"  有效文档数: {len(valid_docs)}")
        print(f"  无效文档数: {invalid_count}")
        print(f"  通过率: {len(valid_docs)/len(docs)*100:.1f}%" if len(docs) > 0 else "  通过率: 0%")

        return valid_docs

    def advanced_clean_case_description(self, text: str) -> str:
        """
        高级数据清洗（可选使用）
        处理更复杂的文本清洗需求

        Args:
            text: 原始文本

        Returns:
            清洗后的文本
        """
        if pandas.isnull(text) or text == "":
            return ""

        # 先执行基础清洗
        text = self.clean_case_description(text)

        if text == "":
            return ""

        # 高级清洗规则

        # 1. 处理常见的无意义前缀
        prefixes_to_remove = [
            r'^案件描述[:：]\s*',
            r'^简要描述[:：]\s*',
            r'^案情[:：]\s*',
            r'^概述[:：]\s*',
            r'^摘要[:：]\s*'
        ]
        for prefix in prefixes_to_remove:
            text = re.sub(prefix, '', text, flags=re.IGNORECASE)

        # 2. 处理重复的词语或短语
        # 去除连续重复的字符（超过3个相同字符）
        text = re.sub(r'(.)\1{3,}', r'\1\1', text)

        # 3. 标准化常见法律术语的格式
        legal_terms = {
            r'人民法院': '人民法院',
            r'当事人': '当事人',
            r'被告人': '被告人',
            r'原告人': '原告人',
            r'申请人': '申请人',
            r'被申请人': '被申请人'
        }

        # 应用法律术语标准化（这里可以根据需要扩展）
        for pattern, replacement in legal_terms.items():
            text = re.sub(pattern, replacement, text)

        # 4. 处理日期格式统一
        # 将各种日期格式统一为标准格式
        date_patterns = [
            (r'(\d{4})年(\d{1,2})月(\d{1,2})日', r'\1年\2月\3日'),
            (r'(\d{4})-(\d{1,2})-(\d{1,2})', r'\1年\2月\3日'),
            (r'(\d{4})/(\d{1,2})/(\d{1,2})', r'\1年\2月\3日')
        ]
        for pattern, replacement in date_patterns:
            text = re.sub(pattern, replacement, text)

        # 5. 处理金额格式
        # 统一金额表示方式
        text = re.sub(r'(\d+)元人民币', r'\1元', text)
        text = re.sub(r'人民币(\d+)元', r'\1元', text)

        # 6. 去除明显的测试数据或占位符
        test_patterns = [
            r'^测试.*',
            r'^test.*',
            r'^示例.*',
            r'^样例.*',
            r'^xxx.*',
            r'^待填写.*',
            r'^暂无.*'
        ]
        for pattern in test_patterns:
            if re.match(pattern, text, flags=re.IGNORECASE):
                return ""

        # 7. 最小长度检查
        if len(text.strip()) < 10:  # 如果清洗后文本太短，可能不是有效描述
            return ""

        return text.strip()

    def check_data_quality(self, df: pandas.DataFrame, filename: str) -> pandas.DataFrame:
        """
        检查数据质量并打印空值信息

        Args:
            df: 要检查的DataFrame
            filename: 文件名，用于日志输出

        Returns:
            过滤后的DataFrame（移除了空值行）
        """
        print(f"\n=== 数据质量检查: {filename} ===")

        # 检查"案件简要描述"列是否存在
        if "案件简要描述" not in df.columns:
            print(f"错误: 文件 {filename} 中没有找到'案件简要描述'列")
            print(f"可用列: {list(df.columns)}")
            return pandas.DataFrame()  # 返回空DataFrame

        # 检测各种类型的空值
        null_mask = df["案件简要描述"].isnull()  # NaN, None
        empty_str_mask = df["案件简要描述"].astype(str).str.strip() == ""  # 空字符串
        combined_mask = null_mask | empty_str_mask

        null_count = combined_mask.sum()

        if null_count > 0:
            print(f"\n发现 {null_count} 行'案件简要描述'列为空值或空字符串:")
            print("=" * 60)

            problem_rows = df[combined_mask]
            for idx, row in problem_rows.iterrows():
                desc_value = row["案件简要描述"]
                if pandas.isnull(desc_value):
                    value_type = "NULL/NaN"
                else:
                    value_type = f"空字符串: '{desc_value}'"

                print(f"行索引 {idx} - {value_type}:")
                # 只显示前几列重要信息，避免输出过长
                display_cols = [col for col in df.columns if col != "案件简要描述"][:5]
                for col in display_cols:
                    print(f"  {col}: {row[col]}")
                print("-" * 40)
        else:
            print("✓ 所有行的'案件简要描述'列都有有效值")

        # 过滤掉空值行
        df_filtered = df[~combined_mask].copy()

        print(f"\n数据过滤统计:")
        print(f"  原始数据行数: {len(df)}")
        print(f"  过滤后行数: {len(df_filtered)}")
        print(f"  移除行数: {null_count}")
        print(f"  数据完整率: {len(df_filtered)/len(df)*100:.1f}%" if len(df) > 0 else "  数据完整率: 0%")

        return df_filtered

    def load_data(self) -> list[Document]:
        cases_doc = []
        total_files = 0
        processed_files = 0

        for file in os.listdir(self.cases_dir):
            if file.endswith(".xlsx"):
                total_files += 1
                print(f"\n{'='*60}")
                print(f"处理文件 {total_files}: {file}")
                print(f"{'='*60}")

                try:
                    df = pandas.read_excel(os.path.join(self.cases_dir, file), engine='openpyxl')
                    print(f"文件基本信息:")
                    print(df.info())

                    # 1. 先进行案件描述的数据清洗
                    df_cleaned = self.clean_dataframe_descriptions(df, file)

                    # 2. 再进行数据质量检查和过滤
                    df_filtered = self.check_data_quality(df_cleaned, file)

                    # 3. 最后进行元数据清洗，确保兼容向量数据库
                    df_final = self.clean_metadata_for_vectorstore(df_filtered, file)

                    if len(df_final) > 0:
                        loader = DataFrameLoader(df_final, page_content_column="案件简要描述")
                        loaded_docs = loader.load()

                        # 验证文档是否适合向量数据库存储
                        validated_docs = self.validate_for_vectorstore(loaded_docs, file)

                        if len(validated_docs) > 0:
                            cases_doc.extend(validated_docs)
                            processed_files += 1
                            print(f"✓ 成功加载 {len(validated_docs)} 个有效文档（原始: {len(loaded_docs)}）")
                        else:
                            print(f"⚠ 警告: 文件 {file} 所有文档验证失败，跳过处理")
                    else:
                        print(f"⚠ 警告: 文件 {file} 清洗和过滤后没有有效数据，跳过处理")

                except Exception as e:
                    print(f"✗ 错误: 处理文件 {file} 时发生异常: {str(e)}")
                    continue

        print(f"\n{'='*60}")
        print(f"数据加载完成统计:")
        print(f"  总文件数: {total_files}")
        print(f"  成功处理: {processed_files}")
        print(f"  失败文件: {total_files - processed_files}")
        print(f"  总文档数: {len(cases_doc)}")
        print(f"{'='*60}")

        return cases_doc

class CaseDataWriter:
    def __init__(self, docs: list[Document]):
        self.docs = docs

    def write(self):
        if not self.docs:
            print("⚠ 警告: 没有文档需要写入向量数据库")
            return

        print(f"\n=== 写入向量数据库 ===")
        print(f"准备写入 {len(self.docs)} 个文档")

        try:
            vector_store = LLMFactory.get_instance().get_case_vector_store()

            # 最后一次安全检查
            safe_docs = []
            for i, doc in enumerate(self.docs):
                try:
                    # 确保page_content不为空
                    if not doc.page_content or doc.page_content.strip() == "":
                        print(f"跳过文档 {i}: page_content为空")
                        continue

                    # 确保metadata中没有NaN值
                    clean_metadata = {}
                    for key, value in doc.metadata.items():
                        if pandas.isnull(value) or str(value).lower() in ['nan', 'none', 'null']:
                            clean_metadata[key] = ""
                        else:
                            clean_metadata[key] = str(value)

                    # 创建清洁的文档
                    clean_doc = Document(
                        page_content=doc.page_content,
                        metadata=clean_metadata
                    )
                    safe_docs.append(clean_doc)

                except Exception as e:
                    print(f"处理文档 {i} 时发生错误: {e}")
                    continue

            if not safe_docs:
                print("✗ 错误: 没有有效文档可以写入")
                return

            print(f"实际写入 {len(safe_docs)} 个文档")

            # 分批写入，避免一次性写入过多数据
            batch_size = 100
            for i in range(0, len(safe_docs), batch_size):
                batch = safe_docs[i:i+batch_size]
                print(f"写入批次 {i//batch_size + 1}: {len(batch)} 个文档")

                vector_store.add_texts(
                    texts=[doc.page_content for doc in batch],
                    metadatas=[doc.metadata for doc in batch]
                )

            print(f"✓ 成功写入所有 {len(safe_docs)} 个文档到向量数据库")

        except Exception as e:
            print(f"✗ 写入向量数据库时发生错误: {e}")
            print("错误详情:")
            import traceback
            traceback.print_exc()
            raise


if __name__ == "__main__":
    # 标准清洗模式
    # loader = CaseDataLoader(use_advanced_cleaning=False)

    # 高级清洗模式
    loader = CaseDataLoader(use_advanced_cleaning=True)

    docs = loader.load_data()
    CaseDataWriter(docs).write()