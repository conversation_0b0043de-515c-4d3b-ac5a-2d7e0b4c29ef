#!/usr/bin/env python3
"""
测试序列化函数的脚本
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from decimal import Decimal
from datetime import datetime
from db_datasource_processor import serialize_for_milvus

def test_serialization():
    """测试序列化函数"""
    
    # 测试数据，模拟错误消息中的数据结构
    test_data = {
        'ID': '2ae1a81d58bb42a2bfc01d9b31d7edbb',
        'matter_data': [
            '2ae1a81d58bb42a2bfc01d9b31d7edbb',
            None,
            '对广告发布单位进行抽查监管',
            '440625019000',
            '02',
            '25',
            '3,4',
            Decimal('0'),
            '2',
            '8cef688829ae74cbf33fbe76296000df',
            'province_20190618',
            '44000000057',
            datetime(2019, 4, 10, 0, 0),
            datetime(2023, 2, 7, 14, 22, 3),
            Decimal('0'),
            Decimal('100'),
            None,
            None,
            '440000',
            'provincecatalogmodel',
            None,
            datetime(2019, 11, 28, 9, 33, 46),
            '20211223012735',
            Decimal('3'),
            '广东省'
        ]
    }
    
    print("原始数据:")
    print(test_data)
    print("\n" + "="*50 + "\n")
    
    # 序列化数据
    try:
        serialized_data = serialize_for_milvus(test_data)
        print("序列化后的数据:")
        print(serialized_data)
        print("\n" + "="*50 + "\n")
        
        # 验证序列化后的数据类型
        print("数据类型验证:")
        def check_types(obj, path=""):
            if isinstance(obj, dict):
                for key, value in obj.items():
                    check_types(value, f"{path}.{key}" if path else key)
            elif isinstance(obj, list):
                for i, item in enumerate(obj):
                    check_types(item, f"{path}[{i}]")
            else:
                print(f"{path}: {type(obj).__name__} = {obj}")
        
        check_types(serialized_data)
        
        print("\n序列化测试成功！")
        return True
        
    except Exception as e:
        print(f"序列化测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    test_serialization()
