from langchain.agents import AgentType
from langchain.sql_database import SQLDatabase
from langchain_community.agent_toolkits import SQLDatabaseToolkit, create_sql_agent
from sqlalchemy import create_engine

from chains import llm, db_connection_string

db_engine = create_engine(db_connection_string)
# 初始化LangChain的SQLDatabase
db = SQLDatabase(db_engine)
# 创建SQLDatabaseToolkit
toolkit = SQLDatabaseToolkit(db=db, llm=llm)

# 创建SQL代理
agent_executor = create_sql_agent(
    llm=llm,
    toolkit=toolkit,
    verbose=True,
    agent_type=AgentType.ZERO_SHOT_REACT_DESCRIPTION
)

# 数据查询
def run_sql(input):
    """使用SQL代理执行自然语言到SQL查询"""
    try:
        # 执行SQL代理
        result = agent_executor.invoke({
            "input": input
        })

        output = result.get("output", "未获得查询结果")

        return {
            "sql_result": output,
            "output": f"【数据查询结果】\n{output}"
        }
    except Exception as e:
        return {
            "sql_result": f"错误: {str(e)}",
            "output": f"【数据查询错误】\n{str(e)}"
        }