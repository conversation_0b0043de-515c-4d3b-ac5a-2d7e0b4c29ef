#!/usr/bin/env python3
"""
测试修复后的数据库查询功能
"""

from langchain_community.utilities import SQLDatabase
from datarw.db_datasource_processor import get_content_ids_from_db, get_matter_info_by_content_ids

def test_database_queries():
    """测试数据库查询功能"""
    
    # 连接数据库
    db = SQLDatabase.from_uri("mysql+pymysql://root:minstone123@192.168.2.154:3306/law_item_sc")
    
    print("=== 测试获取 Content IDs ===")
    content_ids = get_content_ids_from_db(db)
    print(f"获取到的 Content IDs: {content_ids}")
    
    if content_ids:
        print("\n=== 测试获取事项信息 ===")
        matter_info = get_matter_info_by_content_ids(db, content_ids)
        print(f"获取到的事项信息数量: {len(matter_info)}")
        
        for content_id, info_list in matter_info.items():
            print(f"Content ID: {content_id}")
            print(f"  事项数量: {len(info_list)}")
            if info_list:
                print(f"  第一个事项的ID: {info_list[0].get('ID', 'N/A')}")
    else:
        print("未获取到任何 Content IDs")

if __name__ == "__main__":
    test_database_queries()
