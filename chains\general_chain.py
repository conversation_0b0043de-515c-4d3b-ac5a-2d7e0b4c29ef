from langchain_core.output_parsers import StrOutputParser
from langchain_core.prompts import ChatPromptTemplate

from chains import llm

prompt = ChatPromptTemplate.from_messages([
    ("system", "你是一位专业的行政执法助手，请回答用户问题。"),
    ("human", "用户问题：{input}")
])

general_chain = prompt | llm | StrOutputParser()

def general(input: str) -> dict[str, str]:
    response = general_chain.invoke({"input": input})
    return {"output": response}
