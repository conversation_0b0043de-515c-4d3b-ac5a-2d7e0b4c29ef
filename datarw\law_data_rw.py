import os
import re

from dotenv import load_dotenv
from langchain_community.document_loaders import DirectoryLoader, PyPDFLoader
from langchain_core.documents import Document

from llm.minstone_llm import LLMFactory


def _parse_law_name(doc):
    return os.path.basename(doc.metadata['source'])[:-len(".pdf")]


def resplit_docs(docs):
    full_text = "\n".join([p.page_content for p in docs])
    full_text = re.sub(r'－\d+－\n', '', full_text)
    full_text = re.sub(r'第[零一二三四五六七八九十百千]章.*\n', '', full_text)
    # 定义匹配中文法律条款的正则表达式
    pattern = r'(?<=[。(章\n)])\s*(第[零一二三四五六七八九十百千]+条\s+)'
    chunks = re.split(pattern, full_text)
    chunks = chunks[1:]
    chunks = [chunks[i] + chunks[i + 1] if i + 1 < len(chunks) else chunks[i] for i in range(0, len(chunks), 2)]

    procced_chunks = []

    for chunk in chunks:
        match = re.match(r'^(第[零一二三四五六七八九十百千]+条)', chunk)
        procced_chunks.append({
            "text": chunk,
            "metadata": {
                "law_name": "中华人民共和国广告法",
                "article": match.group(1) if match else "未知条款",
            }
        })

    return procced_chunks


class LawDataLoader:
    def __init__(self):
        load_dotenv()
        self.laws_dir = os.getenv('LAWS_DIR')
        self.cases_dir = os.getenv('CASES_DIR')

    def load_data(self) -> list[Document]:
        # 载入法律法规
        laws_loader = DirectoryLoader(path=self.laws_dir, glob="**/*.pdf",
                                      loader_cls=PyPDFLoader,
                                      use_multithreading=True,
                                      show_progress=True)

        return resplit_docs(laws_loader.load())


class LawDataWriter:
    def __init__(self, docs: list[dict]):
        self.docs = docs

    def write(self):
        vector_store = LLMFactory.get_instance().get_law_vector_store()
        docs = self.docs
        vector_store.add_texts(texts=[doc["text"] for doc in docs], metadatas=[doc["metadata"] for doc in docs])


if __name__ == '__main__':
    LawDataWriter(LawDataLoader().load_data()).write()