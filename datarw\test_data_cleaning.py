#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据清洗功能测试脚本
用于测试案件简要描述的数据清洗效果
"""

import pandas as pd
from case_data_rw import CaseDataLoader


def create_test_data():
    """创建测试数据"""
    test_cases = [
        "   这是一个正常的案件描述   ",  # 首尾空格
        "案件描述：原告张三诉被告李四合同纠纷案",  # 带前缀
        "原告    张三    诉被告    李四    合同纠纷案",  # 多余空格
        "原告张三诉被告李四合同纠纷案。。。。",  # 重复标点
        "原告张三诉被告李四合同纠纷案@#$%^&*()",  # 特殊字符
        "2023年12月1日，原告张三向法院提起诉讼",  # 正常日期
        "2023-12-01，原告张三向法院提起诉讼",  # 不同日期格式
        "涉及金额人民币10000元",  # 金额格式
        "涉及金额10000元人民币",  # 不同金额格式
        "测试数据，请忽略",  # 测试数据
        "xxx待填写",  # 占位符
        "短",  # 过短文本
        "",  # 空字符串
        None,  # 空值
        "原告张三张三张三张三诉被告李四",  # 重复字符
        "（）空括号内容（）应该被清理",  # 空括号
        "\t\n\r包含各种空白字符的文本\t\n\r",  # 各种空白字符
    ]
    
    df = pd.DataFrame({
        "案件编号": [f"CASE{i:03d}" for i in range(len(test_cases))],
        "案件简要描述": test_cases,
        "案件类型": ["合同纠纷"] * len(test_cases)
    })
    
    return df


def test_cleaning_methods():
    """测试不同的清洗方法"""
    print("=" * 80)
    print("数据清洗功能测试")
    print("=" * 80)
    
    # 创建测试数据
    test_df = create_test_data()
    
    print("\n原始测试数据:")
    print("-" * 50)
    for idx, desc in enumerate(test_df["案件简要描述"]):
        print(f"{idx:2d}: '{desc}'")
    
    # 测试标准清洗
    print("\n" + "=" * 80)
    print("标准清洗模式测试")
    print("=" * 80)
    
    loader_standard = CaseDataLoader(use_advanced_cleaning=False)
    df_standard = test_df.copy()
    df_cleaned_standard = loader_standard.clean_dataframe_descriptions(df_standard, "test_standard.xlsx")
    
    print("\n标准清洗结果:")
    print("-" * 50)
    for idx, (original, cleaned) in enumerate(zip(test_df["案件简要描述"], df_cleaned_standard["案件简要描述"])):
        if str(original) != str(cleaned):
            print(f"{idx:2d}: '{original}' -> '{cleaned}'")
    
    # 测试高级清洗
    print("\n" + "=" * 80)
    print("高级清洗模式测试")
    print("=" * 80)
    
    loader_advanced = CaseDataLoader(use_advanced_cleaning=True)
    df_advanced = test_df.copy()
    df_cleaned_advanced = loader_advanced.clean_dataframe_descriptions(df_advanced, "test_advanced.xlsx")
    
    print("\n高级清洗结果:")
    print("-" * 50)
    for idx, (original, cleaned) in enumerate(zip(test_df["案件简要描述"], df_cleaned_advanced["案件简要描述"])):
        if str(original) != str(cleaned):
            print(f"{idx:2d}: '{original}' -> '{cleaned}'")
    
    # 对比两种方法的差异
    print("\n" + "=" * 80)
    print("标准清洗 vs 高级清洗 差异对比")
    print("=" * 80)
    
    differences = 0
    for idx, (standard, advanced) in enumerate(zip(df_cleaned_standard["案件简要描述"], df_cleaned_advanced["案件简要描述"])):
        if str(standard) != str(advanced):
            differences += 1
            print(f"{idx:2d}:")
            print(f"  标准: '{standard}'")
            print(f"  高级: '{advanced}'")
            print()
    
    if differences == 0:
        print("两种清洗方法在测试数据上结果相同")
    
    print(f"\n总结:")
    print(f"  原始数据行数: {len(test_df)}")
    print(f"  标准清洗后有效行数: {sum(1 for x in df_cleaned_standard['案件简要描述'] if x != '')}")
    print(f"  高级清洗后有效行数: {sum(1 for x in df_cleaned_advanced['案件简要描述'] if x != '')}")
    print(f"  两种方法差异数: {differences}")


if __name__ == "__main__":
    test_cleaning_methods()
