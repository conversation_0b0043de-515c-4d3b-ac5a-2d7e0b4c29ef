#!/usr/bin/env python3
"""
测试 Milvus 动态字段大小限制修复
"""

import json
import logging
from datarw.db_datasource_processor import (
    serialize_for_milvus, 
    get_json_size, 
    truncate_large_fields,
    MILVUS_DYNAMIC_FIELD_MAX_SIZE
)

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


def test_json_size_calculation():
    """测试JSON大小计算"""
    print("=== 测试JSON大小计算 ===")
    
    test_data = {
        "simple_string": "Hello World",
        "number": 12345,
        "list": [1, 2, 3, 4, 5],
        "nested": {
            "key1": "value1",
            "key2": "value2"
        }
    }
    
    size = get_json_size(test_data)
    actual_json = json.dumps(test_data, ensure_ascii=False)
    actual_size = len(actual_json.encode('utf-8'))
    
    print(f"计算大小: {size} 字节")
    print(f"实际大小: {actual_size} 字节")
    print(f"JSON内容: {actual_json}")
    print(f"大小计算正确: {size == actual_size}")
    print()


def test_large_string_truncation():
    """测试大字符串截断"""
    print("=== 测试大字符串截断 ===")
    
    # 创建一个超过限制的大字符串
    large_string = "这是一个很长的字符串。" * 10000  # 约 200KB
    original_size = len(large_string.encode('utf-8'))
    
    print(f"原始字符串大小: {original_size} 字节")
    print(f"Milvus限制: {MILVUS_DYNAMIC_FIELD_MAX_SIZE} 字节")
    
    truncated = truncate_large_fields(large_string)
    truncated_size = len(truncated.encode('utf-8'))
    
    print(f"截断后大小: {truncated_size} 字节")
    print(f"截断后内容预览: {truncated[:100]}...")
    print(f"是否在限制内: {truncated_size <= MILVUS_DYNAMIC_FIELD_MAX_SIZE}")
    print()


def test_large_list_truncation():
    """测试大列表截断"""
    print("=== 测试大列表截断 ===")
    
    # 创建一个包含大量数据的列表
    large_list = []
    for i in range(1000):
        large_list.append({
            'ID': f'id_{i}',
            'matter_data': [f'data_{j}' for j in range(100)]  # 每个元素包含100个子项
        })
    
    original_size = get_json_size(large_list)
    print(f"原始列表大小: {original_size} 字节")
    print(f"原始列表长度: {len(large_list)}")
    
    truncated = truncate_large_fields(large_list)
    truncated_size = get_json_size(truncated)
    
    print(f"截断后大小: {truncated_size} 字节")
    print(f"截断后长度: {len(truncated)}")
    print(f"是否在限制内: {truncated_size <= MILVUS_DYNAMIC_FIELD_MAX_SIZE}")
    print()


def test_large_dict_truncation():
    """测试大字典截断"""
    print("=== 测试大字典截断 ===")
    
    # 创建一个包含大量键值对的字典
    large_dict = {}
    for i in range(1000):
        large_dict[f'key_{i}'] = {
            'id': f'value_{i}',
            'data': [f'item_{j}' for j in range(50)]
        }
    
    original_size = get_json_size(large_dict)
    print(f"原始字典大小: {original_size} 字节")
    print(f"原始字典键数量: {len(large_dict)}")
    
    truncated = truncate_large_fields(large_dict)
    truncated_size = get_json_size(truncated)
    
    print(f"截断后大小: {truncated_size} 字节")
    print(f"截断后键数量: {len(truncated)}")
    print(f"是否包含截断标记: {'_truncated' in truncated}")
    print(f"是否在限制内: {truncated_size <= MILVUS_DYNAMIC_FIELD_MAX_SIZE}")
    print()


def test_serialize_and_truncate():
    """测试序列化和截断的完整流程"""
    print("=== 测试完整序列化和截断流程 ===")
    
    # 模拟实际的事项列表数据
    matter_list = []
    for i in range(100):
        matter_list.append({
            'ID': f'755a7bc3ffa6eed4c38ba830a8c6c42f_{i}',
            'matter_data': [
                f'755a7bc3ffa6eed4c38ba830a8c6c42f_{i}',
                None,
                f'对认证证书注销、撤销或者暂停期间，不符合认证要求的产品，继续出厂、销售、或者在其他经营活动中使用的行为的行政处罚_{i}',
                '440225572000',
                '01',
                '25',
                '3,4',
                0.0,
                '2',
                '44415919c720bc9cb512d3160403e14d',
                'system',
                '44000000057',
                '2019-06-03T00:00:00',
                '2025-06-12T15:56:46',
                0.0,
                100.0,
                None,
                '1',
                '440000',
                'provincecatalogmodel',
                None,
                '2019-10-22T05:59:59',
                '20250612155647',
                1.0,
                '广东省'
            ] * 10  # 重复数据以增加大小
        })
    
    # 序列化
    serialized = serialize_for_milvus(matter_list)
    serialized_size = get_json_size(serialized)
    
    print(f"序列化后大小: {serialized_size} 字节")
    print(f"是否超过限制: {serialized_size > MILVUS_DYNAMIC_FIELD_MAX_SIZE}")
    
    if serialized_size > MILVUS_DYNAMIC_FIELD_MAX_SIZE:
        print("数据过大，正在截断...")
        truncated = truncate_large_fields(serialized)
        truncated_size = get_json_size(truncated)
        
        print(f"截断后大小: {truncated_size} 字节")
        print(f"是否在限制内: {truncated_size <= MILVUS_DYNAMIC_FIELD_MAX_SIZE}")
    
    print()


def main():
    """主测试函数"""
    print(f"Milvus 动态字段大小限制: {MILVUS_DYNAMIC_FIELD_MAX_SIZE} 字节")
    print("=" * 60)
    
    test_json_size_calculation()
    test_large_string_truncation()
    test_large_list_truncation()
    test_large_dict_truncation()
    test_serialize_and_truncate()
    
    print("所有测试完成！")


if __name__ == "__main__":
    main()
